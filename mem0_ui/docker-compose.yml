services:
  mem0-ui:
    build:
      context: .
      dockerfile: Dockerfile
    image: mem0/mem0-ui:latest
    container_name: mem0-ui
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      # 修复外部访问问题：容器内部使用服务名访问API，浏览器端使用localhost访问
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8000}
      - NEXT_PUBLIC_MEM0_API_URL=${NEXT_PUBLIC_MEM0_API_URL:-http://localhost:8000}
      # 服务端API调用使用容器网络内的服务名
      - MEM0_API_URL=${MEM0_API_URL:-http://mem0-api:8000}
      - QDRANT_URL=${QDRANT_URL:-http://mem0-qdrant:6333}
      - NEXT_PUBLIC_USER_ID=${NEXT_PUBLIC_USER_ID:-default}
      - NEXT_PUBLIC_DISABLE_MSW=${NEXT_PUBLIC_DISABLE_MSW:-true}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - mem0-network
    # 添加额外的主机配置以支持外部API访问
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  mem0-network:
    external: true
