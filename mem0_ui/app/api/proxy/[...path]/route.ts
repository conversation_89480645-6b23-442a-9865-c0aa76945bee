import { NextRequest, NextResponse } from 'next/server';

const MEM0_API_URL = process.env.MEM0_API_URL || 'http://mem0-api:8000';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleRequest(request, params, 'GET');
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleRequest(request, params, 'POST');
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleRequest(request, params, 'PUT');
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleRequest(request, params, 'DELETE');
}

async function handleRequest(
  request: NextRequest,
  { path }: { path: string[] },
  method: string
) {
  try {
    const apiPath = path.join('/');
    const url = new URL(request.url);
    const searchParams = url.searchParams.toString();
    
    // 构建完整的API URL
    const targetUrl = `${MEM0_API_URL}/${apiPath}${searchParams ? `?${searchParams}` : ''}`;
    
    console.log(`[API Proxy] ${method} ${targetUrl}`);
    
    // 准备请求选项
    const requestOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        // 传递所有相关头部，除了Host
        ...Object.fromEntries(
          Object.entries(request.headers).filter(([key]) => 
            !['host', 'content-length'].includes(key.toLowerCase())
          )
        ),
      },
    };
    
    // 如果有请求体，添加到请求中
    if (method !== 'GET' && method !== 'HEAD') {
      try {
        const body = await request.text();
        if (body) {
          requestOptions.body = body;
        }
      } catch (error) {
        console.warn('[API Proxy] Failed to read request body:', error);
      }
    }
    
    // 发送请求到Mem0 API
    const response = await fetch(targetUrl, requestOptions);
    
    // 获取响应数据
    const responseData = await response.text();
    
    console.log(`[API Proxy] Response ${response.status} from ${targetUrl}`);
    
    // 创建响应并设置CORS头部
    const nextResponse = new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        // 传递其他响应头部
        ...Object.fromEntries(
          Array.from(response.headers.entries()).filter(([key]) => 
            !['content-length', 'transfer-encoding'].includes(key.toLowerCase())
          )
        ),
      },
    });
    
    return nextResponse;
    
  } catch (error) {
    console.error('[API Proxy] Error:', error);
    
    return NextResponse.json(
      { 
        error: 'Proxy request failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        targetUrl: `${MEM0_API_URL}/${path.join('/')}`
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      }
    );
  }
}

// 处理CORS预检请求
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}